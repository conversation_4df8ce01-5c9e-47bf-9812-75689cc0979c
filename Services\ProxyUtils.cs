﻿using System.Net;

namespace Shumei.Services
{
    public class ProxyUtils
    {
        public static readonly HttpClient Http = new HttpClient();

        /// <summary>
        /// 获取代理IP
        /// </summary>
        /// <returns></returns>
        public static async Task<WebProxy> GetProxyIp()
        {
#if DEBUG
            return null;
#endif
            var ip = await Http.GetStringAsync("http://api.dnsi.cn/api/proxy/getproxy");
            return string.IsNullOrEmpty(ip) ? null : new WebProxy(ip);
    
        }
    }
}

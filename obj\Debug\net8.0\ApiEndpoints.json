[{"ContainingType": "Shumei.Controllers.ShuMeiController", "Method": "<PERSON><PERSON>", "RelativePath": "ShuMei/Device", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organization", "Type": "System.String", "IsRequired": false}, {"Name": "appId", "Type": "System.String", "IsRequired": false}, {"Name": "key", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Shumei.Controllers.ShuMeiController", "Method": "Slide", "RelativePath": "ShuMei/Slide", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organization", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]
{"format": 1, "restore": {"E:\\Code\\ShuMei\\Api\\Shumei\\Shumei.csproj": {}}, "projects": {"E:\\Code\\ShuMei\\Api\\Shumei\\Shumei.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\ShuMei\\Api\\Shumei\\Shumei.csproj", "projectName": "<PERSON><PERSON>", "projectPath": "E:\\Code\\ShuMei\\Api\\Shumei\\Shumei.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Code\\ShuMei\\Api\\Shumei\\obj\\publish\\linux-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4_.runtime.ubuntu.20.04-x64": {"target": "Package", "version": "[4.10.0.20240616, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[8.0.13, 8.0.13]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}}
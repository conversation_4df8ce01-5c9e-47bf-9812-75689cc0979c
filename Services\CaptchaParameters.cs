using System;

namespace ShumeiCaptcha
{
    /// <summary>
    /// 数美验证码参数模型
    /// </summary>
    public class CaptchaParameters
    {
        /// <summary>
        /// 组织标识
        /// </summary>
        public string Organization { get; set; }

        /// <summary>
        /// 验证码UUID
        /// </summary>
        public string CaptchaUuid { get; set; }

        /// <summary>
        /// 协议版本
        /// </summary>
        public string Protocol { get; set; }

        /// <summary>
        /// 操作系统类型
        /// </summary>
        public string OsType { get; set; }

        /// <summary>
        /// 具体操作系统
        /// </summary>
        public string ActOs { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string RVersion { get; set; }

        /// <summary>
        /// SDK版本
        /// </summary>
        public string SdkVer { get; set; }

        /// <summary>
        /// 回调函数名
        /// </summary>
        public string Callback { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string Rid { get; set; }

        /// <summary>
        /// 鼠标行为数据 (使用getEncryptContent加密)
        /// </summary>
        public string Vo { get; set; }

        /// <summary>
        /// 硬件指纹数据 (使用getEncryptContent加密)
        /// </summary>
        public string Hg { get; set; }

        /// <summary>
        /// 时间戳数据 (使用getEncryptContent加密)
        /// </summary>
        public string Qt { get; set; }

        /// <summary>
        /// 轨迹数据 (使用5|2|4|3|0|1加密)
        /// </summary>
        public string Th { get; set; }

        /// <summary>
        /// 屏幕信息 (使用5|2|4|3|0|1加密)
        /// </summary>
        public string Lf { get; set; }

        /// <summary>
        /// 浏览器信息 (使用getEncryptContent加密)
        /// </summary>
        public string Ny { get; set; }

        /// <summary>
        /// 设备指纹 (使用getEncryptContent加密)
        /// </summary>
        public string Gg { get; set; }

        /// <summary>
        /// 浏览器签名 (使用5|2|4|3|0|1加密)
        /// </summary>
        public string Bs { get; set; }

        /// <summary>
        /// 屏幕锁定信息 (使用getEncryptContent加密)
        /// </summary>
        public string Sl { get; set; }

        /// <summary>
        /// 表单数据 (使用5|2|4|3|0|1加密)
        /// </summary>
        public string Fm { get; set; }

        /// <summary>
        /// 行为队列 (使用getEncryptContent加密)
        /// </summary>
        public string Bq { get; set; }

        /// <summary>
        /// 触摸操作 (使用5|2|4|3|0|1加密)
        /// </summary>
        public string To { get; set; }

        /// <summary>
        /// YH数据 (使用getEncryptContent加密)
        /// </summary>
        public string Yh { get; set; }
    }

    /// <summary>
    /// 验证响应模型
    /// </summary>
    public class CaptchaResponse
    {
        /// <summary>
        /// 响应代码
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 风险等级
        /// </summary>
        public string RiskLevel { get; set; }

        /// <summary>
        /// 是否验证成功
        /// </summary>
        public bool IsSuccess => Code == 1100 && RiskLevel == "PASS";
    }
}

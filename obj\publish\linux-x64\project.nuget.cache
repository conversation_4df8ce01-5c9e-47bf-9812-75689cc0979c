{"version": 2, "dgSpecHash": "ci7PTxWmdgE=", "success": true, "projectFilePath": "E:\\Code\\ShuMei\\Api\\Shumei\\Shumei.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4\\4.10.0.20241108\\opencvsharp4.4.10.0.20241108.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opencvsharp4_.runtime.ubuntu.20.04-x64\\4.10.0.20240616\\opencvsharp4_.runtime.ubuntu.20.04-x64.4.10.0.20240616.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.linux-x64\\8.0.13\\microsoft.netcore.app.host.linux-x64.8.0.13.nupkg.sha512"], "logs": []}
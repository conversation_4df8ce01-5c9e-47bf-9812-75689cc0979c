using Microsoft.AspNetCore.Mvc;
using Shumei.Services;


namespace Shumei.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class ShuMeiController : ControllerBase
{


    [HttpGet]
    public async Task<string> Slide(string organization)
    {
        var ip = HttpContext.Connection.RemoteIpAddress?.ToString();
        var result = await ShuMeiSlide.Recognize(organization);
        Console.WriteLine($"Slide\t{ip}��{result}");
        return result;
    }

    [HttpGet]
    public async Task<string> Device(string organization, string? appId, string? key)
    {
        if (organization == "qvdTr7A9k1DIU04ha4eP")
        {
            if (string.IsNullOrEmpty(appId))
                appId = "default";
            if (string.IsNullOrEmpty(key))
                key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAlw8ebprQJZ+ZrO7cZ+uoOH6n3sC1ZY2E1vqipxLrOrEGFFWFvymyhmiD4jlNmI0eZsIxO/NJDhTLn35qHyGAT7VFn+lD2Qnjd3gu8Y0av1trny7DeMEAyyEZPT/JJSN1BLaLGERbYGOwD5ujqqoB1hWbRuT41FoIl9KXfGAm4wIDAQAB";
        }
        var ip = HttpContext.Connection.RemoteIpAddress?.ToString();
        var result =  await ShuMeiDevice.DeviceProfile(organization, appId, key);
        Console.WriteLine($"Device\t{ip}��{result}");
        return result;
    }


}

using System;
using System.Threading.Tasks;
using Shumei.Services;

namespace ShumeiCaptcha
{
    /// <summary>
    /// 测试滑动验证码集成
    /// </summary>
    class TestSlideIntegration
    {
        static async Task TestMain(string[] args)
        {
            Console.WriteLine("数美滑动验证码集成测试");
            Console.WriteLine("========================");

            try
            {
                // 测试组织ID
                string organization = "d6tpAY1oV0Kv5jRSgxQr";
                
                Console.WriteLine($"正在测试组织: {organization}");
                Console.WriteLine("开始滑动验证码识别...");
                
                // 调用滑动验证码识别
                var result = await ShuMeiSlide.Recognize(organization);
                
                if (!string.IsNullOrEmpty(result))
                {
                    Console.WriteLine($"✅ 滑动验证码识别成功!");
                    Console.WriteLine($"返回的RID: {result}");
                }
                else
                {
                    Console.WriteLine("❌ 滑动验证码识别失败");
                    Console.WriteLine("可能的原因:");
                    Console.WriteLine("1. 参数生成算法需要更新");
                    Console.WriteLine("2. 固定加密参数已过期");
                    Console.WriteLine("3. 数美检测到了自动化行为");
                    Console.WriteLine("4. 网络连接问题");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 发生异常: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

/// <summary>
/// 滑动验证码问题诊断工具
/// </summary>
public static class SlideDiagnostics
{
    /// <summary>
    /// 诊断滑动验证码问题
    /// </summary>
    public static async Task DiagnoseSlideIssues(string organization)
    {
        Console.WriteLine("🔍 开始诊断滑动验证码问题...");
        
        // 1. 测试基础参数生成
        Console.WriteLine("1. 测试基础参数生成器...");
        try
        {
            var generator = new ShumeiCaptcha.ShumeiCaptchaGenerator();
            var parameters = generator.GenerateParameters(organization, "test_uuid");
            Console.WriteLine("   ✅ 基础参数生成正常");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 基础参数生成失败: {ex.Message}");
            return;
        }
        
        // 2. 测试注册接口
        Console.WriteLine("2. 测试滑动验证码注册接口...");
        try
        {
            using var httpClient = new HttpClient();
            var time = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            string url = $"https://captcha1.fengkongcloud.cn/ca/v1/register?model=slide&sdkver=1.1.3&callback=sm_{time}&rversion=1.0.1&appId=review-panel&lang=en&data=%7B%7D&organization={organization}&channel=DEFAULT";
            
            var response = await httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("   ✅ 注册接口响应正常");
                Console.WriteLine($"   响应内容: {content.Substring(0, Math.Min(200, content.Length))}...");
                
                // 检查是否包含必要的字段
                if (content.Contains("\"bg\"") && content.Contains("\"fg\"") && content.Contains("\"rid\""))
                {
                    Console.WriteLine("   ✅ 响应包含必要的图片和RID信息");
                }
                else
                {
                    Console.WriteLine("   ⚠️  响应缺少必要的图片或RID信息");
                }
            }
            else
            {
                Console.WriteLine($"   ❌ 注册接口请求失败: {response.StatusCode}");
                Console.WriteLine($"   响应内容: {content}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 注册接口测试异常: {ex.Message}");
        }
        
        // 3. 测试参数加密
        Console.WriteLine("3. 测试参数加密功能...");
        try
        {
            // 测试DES加密
            var testData = "0.1234";
            var testKey = "59fcff86";
            var encrypted = TestDesEncrypt(testData, testKey);
            
            if (!string.IsNullOrEmpty(encrypted))
            {
                Console.WriteLine("   ✅ DES加密功能正常");
                Console.WriteLine($"   测试加密结果: {encrypted}");
            }
            else
            {
                Console.WriteLine("   ❌ DES加密功能异常");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 参数加密测试异常: {ex.Message}");
        }
        
        Console.WriteLine("🔍 诊断完成");
    }
    
    /// <summary>
    /// 测试DES加密
    /// </summary>
    private static string TestDesEncrypt(string plainText, string key)
    {
        try
        {
            byte[] keyBytes = System.Text.Encoding.UTF8.GetBytes(key);
            byte[] plainBytes = System.Text.Encoding.UTF8.GetBytes(plainText);

            using var des = System.Security.Cryptography.DES.Create();
            des.Mode = System.Security.Cryptography.CipherMode.ECB;
            des.Padding = System.Security.Cryptography.PaddingMode.Zeros;
            des.Key = keyBytes;

            using var encryptor = des.CreateEncryptor();
            byte[] resultBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
            return Convert.ToBase64String(resultBytes);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"DES加密异常: {ex.Message}");
            return string.Empty;
        }
    }
}

/// <summary>
/// 参数对比工具
/// </summary>
public static class ParameterComparison
{
    /// <summary>
    /// 对比新旧参数生成的差异
    /// </summary>
    public static void CompareParameters(string organization)
    {
        Console.WriteLine("📊 对比新旧参数生成差异...");
        
        try
        {
            // 生成新参数
            var generator = new ShumeiCaptcha.ShumeiCaptchaGenerator();
            var newParams = generator.GenerateParameters(organization, "test_uuid");
            
            Console.WriteLine("新参数生成器生成的参数:");
            Console.WriteLine($"  Vo: {newParams.Vo}");
            Console.WriteLine($"  Hg: {newParams.Hg}");
            Console.WriteLine($"  Qt: {newParams.Qt}");
            Console.WriteLine($"  Th: {newParams.Th}");
            Console.WriteLine($"  Lf: {newParams.Lf}");
            Console.WriteLine();
            
            Console.WriteLine("旧版本固定参数:");
            Console.WriteLine("  aj: Z8JptdSbQHg%3D");
            Console.WriteLine("  uc: b8IY1XIB1iA%3D");
            Console.WriteLine("  jp: Wq4jwGqOHYM%3D");
            Console.WriteLine("  wz: ufdT5h7SVes%3D");
            Console.WriteLine("  sy: lN908%2F15DcI%3D");
            Console.WriteLine();
            
            Console.WriteLine("💡 建议:");
            Console.WriteLine("1. 如果新参数无效，可能需要更新固定参数");
            Console.WriteLine("2. 考虑从数美服务器动态获取这些固定参数");
            Console.WriteLine("3. 检查参数的时效性和会话关联性");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 参数对比异常: {ex.Message}");
        }
    }
}

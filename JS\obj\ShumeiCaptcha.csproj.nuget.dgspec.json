{"format": 1, "restore": {"E:\\Code\\ShuMei\\Api\\Shumei\\JS\\ShumeiCaptcha.csproj": {}}, "projects": {"E:\\Code\\ShuMei\\Api\\Shumei\\JS\\ShumeiCaptcha.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Code\\ShuMei\\Api\\Shumei\\JS\\ShumeiCaptcha.csproj", "projectName": "ShumeiCap<PERSON><PERSON>", "projectPath": "E:\\Code\\ShuMei\\Api\\Shumei\\JS\\ShumeiCaptcha.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Code\\ShuMei\\Api\\Shumei\\JS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.8.0.20230708, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.8.0.20230708, )"}, "System.Text.Json": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}
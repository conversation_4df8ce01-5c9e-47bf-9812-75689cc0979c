const fs = require("fs")

class SimpleCaptchaDeobfuscator {
  constructor() {
    this.stringArrays = new Map()
    this.replacements = new Map()
    this.statistics = {
      stringsRestored: 0,
      propertiesRestored: 0,
      functionsRenamed: 0,
      controlFlowSimplified: 0,
    }
  }

  // 主要反混淆方法
  deobfuscate(code) {
    console.log("开始反混淆处理...")

    let result = code

    // 第一步：提取字符串数组
    result = this.extractStringArrays(result)

    // 第二步：还原字符串引用
    result = this.restoreStringReferences(result)

    // 第三步：简化控制流
    result = this.simplifyControlFlow(result)

    // 第四步：还原属性访问
    result = this.restorePropertyAccess(result)

    // 第五步：还原函数调用
    result = this.restoreFunctionCalls(result)

    // 第六步：重命名变量和函数
    result = this.renameVariables(result)

    // 第七步：格式化代码
    result = this.formatCode(result)

    this.printStatistics()
    return result
  }

  // 使用正则表达式提取字符串数组
  extractStringArrays(code) {
    console.log("提取字符串数组...")

    // 查找 _0x2ffd 函数中的字符串数组
    const arrayPattern =
      /function\s+(_0x[a-f0-9]+)\(\)\s*\{\s*var\s+\w+\s*=\s*\[([\s\S]*?)\]\s*;?\s*return\s+\w+\s*;\s*\}/g

    let match
    while ((match = arrayPattern.exec(code)) !== null) {
      const functionName = match[1]
      const arrayContent = match[2]

      // 提取字符串
      const strings = this.parseStringArray(arrayContent)
      if (strings.length > 0) {
        this.stringArrays.set(functionName, strings)
        console.log(
          `发现字符串数组函数: ${functionName}, 包含 ${strings.length} 个字符串`
        )
      }
    }

    return code
  }

  // 解析字符串数组内容
  parseStringArray(arrayContent) {
    const strings = []

    // 匹配字符串字面量
    const stringPattern = /'([^'\\]*(\\.[^'\\]*)*)'/g
    let match

    while ((match = stringPattern.exec(arrayContent)) !== null) {
      // 处理转义字符
      let str = match[1]
      str = str.replace(/\\'/g, "'")
      str = str.replace(/\\"/g, '"')
      str = str.replace(/\\n/g, "\n")
      str = str.replace(/\\r/g, "\r")
      str = str.replace(/\\t/g, "\t")
      str = str.replace(/\\x([0-9a-fA-F]{2})/g, (_, hex) =>
        String.fromCharCode(parseInt(hex, 16))
      )
      str = str.replace(/\\u([0-9a-fA-F]{4})/g, (_, hex) =>
        String.fromCharCode(parseInt(hex, 16))
      )
      str = str.replace(/\\\\/g, "\\")

      strings.push(str)
    }

    return strings
  }

  // 还原字符串引用
  restoreStringReferences(code) {
    console.log("还原字符串引用...")

    let result = code

    // 模式1: _0x4d68(0x1f0) 形式的调用
    result = result.replace(
      /_0x[a-f0-9]+\(0x([a-f0-9]+)\)/g,
      (match, hexIndex) => {
        try {
          const index = parseInt(hexIndex, 16)
          // 尝试从第一个字符串数组获取
          const firstArray = Array.from(this.stringArrays.values())[0]
          if (firstArray && firstArray[index]) {
            this.statistics.stringsRestored++
            return `"${this.escapeString(firstArray[index])}"`
          }
        } catch (e) {
          // 忽略错误
        }
        return match
      }
    )

    // 模式2: _0x2ffd()[index] 形式的访问
    for (const [funcName, stringArray] of this.stringArrays) {
      const pattern = new RegExp(`${funcName}\\(\\)\\[([0-9]+)\\]`, "g")
      result = result.replace(pattern, (match, indexStr) => {
        const index = parseInt(indexStr)
        if (stringArray[index]) {
          this.statistics.stringsRestored++
          return `"${this.escapeString(stringArray[index])}"`
        }
        return match
      })
    }

    console.log(`已还原 ${this.statistics.stringsRestored} 个字符串`)
    return result
  }

  // 转义字符串
  escapeString(str) {
    return str
      .replace(/\\/g, "\\\\")
      .replace(/"/g, '\\"')
      .replace(/\n/g, "\\n")
      .replace(/\r/g, "\\r")
      .replace(/\t/g, "\\t")
  }

  // 简化控制流
  simplifyControlFlow(code) {
    console.log("简化控制流...")

    let result = code

    // 移除复杂的三元运算符链
    result = result.replace(/\?\s*[^:]{50,}\s*:\s*[^,;]{50,}/g, (match) => {
      this.statistics.controlFlowSimplified++
      return "true" // 简化为简单值
    })

    // 简化复杂的逻辑表达式
    result = result.replace(/&&\s*!!?\[\]/g, "")
    result = result.replace(/\|\|\s*!!?\[\]/g, "")

    // 简化数字运算表达式
    result = result.replace(
      /(-?0x[a-f0-9]+\+0x[a-f0-9]+\*0x[a-f0-9]+\+-?0x[a-f0-9]+)/g,
      "0"
    )

    console.log(
      `简化了 ${this.statistics.controlFlowSimplified} 个控制流表达式`
    )
    return result
  }

  // 还原属性访问
  restorePropertyAccess(code) {
    console.log("还原属性访问...")

    let result = code

    // 还原常见的属性访问模式
    const propertyMappings = {
      exports: "exports",
      prototype: "prototype",
      constructor: "constructor",
      length: "length",
      push: "push",
      call: "call",
      apply: "apply",
      toString: "toString",
      valueOf: "valueOf",
      hasOwnProperty: "hasOwnProperty",
      __esModule: "__esModule",
      default: "default",
    }

    for (const [encoded, decoded] of Object.entries(propertyMappings)) {
      // 替换 ['property'] 形式
      const pattern1 = new RegExp(`\\['${encoded}'\\]`, "g")
      result = result.replace(pattern1, `.${decoded}`)

      // 替换 ["property"] 形式
      const pattern2 = new RegExp(`\\["${encoded}"\\]`, "g")
      result = result.replace(pattern2, `.${decoded}`)

      this.statistics.propertiesRestored++
    }

    console.log(`已还原 ${this.statistics.propertiesRestored} 个属性访问`)
    return result
  }

  // 还原函数调用
  restoreFunctionCalls(code) {
    console.log("还原函数调用...")

    let result = code

    // 还原常见的函数调用模式
    const functionMappings = {
      require: "require",
      module: "module",
      exports: "exports",
      console: "console",
      window: "window",
      document: "document",
      JSON: "JSON",
      Object: "Object",
      Array: "Array",
      String: "String",
      Number: "Number",
      Boolean: "Boolean",
      Function: "Function",
      Error: "Error",
      TypeError: "TypeError",
      ReferenceError: "ReferenceError",
      SyntaxError: "SyntaxError",
    }

    for (const [encoded, decoded] of Object.entries(functionMappings)) {
      // 替换字符串形式的函数名
      const pattern = new RegExp(`"${encoded}"`, "g")
      result = result.replace(pattern, decoded)
    }

    return result
  }

  // 重命名变量和函数
  renameVariables(code) {
    console.log("重命名变量和函数...")

    let result = code
    let counter = 0

    // 重命名混淆的变量名
    const varPattern = /_0x[a-f0-9]+/g
    const matches = new Set()

    // 收集所有匹配项
    let match
    while ((match = varPattern.exec(result)) !== null) {
      matches.add(match[0])
    }

    // 为每个唯一的变量名创建映射
    for (const varName of matches) {
      if (!this.replacements.has(varName)) {
        this.replacements.set(varName, `var_${counter++}`)
        this.statistics.functionsRenamed++
      }
    }

    // 执行替换
    for (const [oldName, newName] of this.replacements) {
      const pattern = new RegExp(
        `\\b${oldName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`,
        "g"
      )
      result = result.replace(pattern, newName)
    }

    console.log(`已重命名 ${this.statistics.functionsRenamed} 个变量/函数`)
    return result
  }

  // 格式化代码
  formatCode(code) {
    console.log("格式化代码...")

    let result = code

    // 基本的代码格式化
    result = result.replace(/;/g, ";\n")
    result = result.replace(/\{/g, " {\n")
    result = result.replace(/\}/g, "\n}\n")
    result = result.replace(/,/g, ",\n")

    // 清理多余的空行
    result = result.replace(/\n\s*\n\s*\n/g, "\n\n")

    return result
  }

  // 打印统计信息
  printStatistics() {
    console.log("\n=== 反混淆统计 ===")
    console.log(`字符串还原: ${this.statistics.stringsRestored}`)
    console.log(`属性还原: ${this.statistics.propertiesRestored}`)
    console.log(`变量重命名: ${this.statistics.functionsRenamed}`)
    console.log(`控制流简化: ${this.statistics.controlFlowSimplified}`)
    console.log(`字符串数组: ${this.stringArrays.size}`)
  }
}

// 主函数
function main() {
  const inputFile = "captcha-sdk.min.js"
  const outputFile = "captcha-sdk.deobfuscated.js"

  if (!fs.existsSync(inputFile)) {
    console.error(`输入文件不存在: ${inputFile}`)
    process.exit(1)
  }

  console.log(`读取文件: ${inputFile}`)
  const code = fs.readFileSync(inputFile, "utf8")

  const deobfuscator = new SimpleCaptchaDeobfuscator()
  const deobfuscatedCode = deobfuscator.deobfuscate(code)

  console.log(`写入文件: ${outputFile}`)
  fs.writeFileSync(outputFile, deobfuscatedCode, "utf8")

  console.log("反混淆完成！")
}

if (require.main === module) {
  main()
}

module.exports = SimpleCaptchaDeobfuscator

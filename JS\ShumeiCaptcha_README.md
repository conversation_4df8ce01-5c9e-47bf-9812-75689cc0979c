# 数美验证码参数生成器 - C# 实现

基于对 `captcha-sdk.min.js` 的逆向分析，用 C# 复刻数美验证码的所有参数生成算法。

## 项目概述

本项目通过深度分析数美验证码的 JavaScript SDK，还原了其参数生成的核心算法，并用 C# 重新实现。支持生成所有必要的验证参数，包括加密的行为数据、设备指纹、时间戳等。

## 核心功能

### 1. 参数生成算法

#### 主要加密函数
- **`getEncryptContent`**: 使用 SHA256 + Base64 的加密方式
- **`5|2|4|3|0|1`**: 使用 AES + 自定义处理的加密方式

#### 支持的参数类型

**使用 `getEncryptContent` 加密的参数:**
- `vo` - 鼠标行为数据
- `hg` - 硬件指纹信息
- `qt` - 时间戳数据
- `ny` - 浏览器信息
- `gg` - 设备指纹
- `sl` - 屏幕锁定信息
- `bq` - 行为队列数据
- `yh` - YH数据

**使用 `5|2|4|3|0|1` 加密的参数:**
- `th` - 鼠标轨迹数据
- `lf` - 屏幕信息
- `bs` - 浏览器签名
- `fm` - 表单数据
- `to` - 触摸操作数据

### 2. 数据生成

- **设备指纹**: 生成唯一的设备标识
- **会话ID**: 生成符合格式的会话标识
- **鼠标轨迹**: 模拟真实的鼠标移动轨迹
- **屏幕信息**: 生成屏幕分辨率、颜色深度等信息
- **浏览器指纹**: 包括 Canvas、WebGL、音频指纹等
- **硬件信息**: CPU、GPU、字体列表等硬件特征

## 使用方法

### 基本用法

```csharp
using ShumeiCaptcha;

// 创建参数生成器
var generator = new ShumeiCaptchaGenerator();

// 生成验证参数
var parameters = generator.GenerateParameters(
    organization: "d6tpAY1oV0Kv5jRSgxQr",
    captchaUuid: "20250729181928QF56WSstRAiMMcfRDN"
);

// 构建验证URL
var verifyUrl = generator.BuildVerifyUrl(parameters);

Console.WriteLine($"验证URL: {verifyUrl}");
```

### 高级用法 - 自定义行为数据

```csharp
// 创建高级生成器
var advancedGenerator = new AdvancedCaptchaGenerator();

// 自定义鼠标行为
var mouseBehavior = new MouseBehaviorData
{
    Points = new List<MousePoint>
    {
        new MousePoint { X = 100, Y = 200, Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(), Type = "move" },
        new MousePoint { X = 150, Y = 250, Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + 100, Type = "click" }
    },
    StartTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
    EndTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() + 1000
};

// 生成带有自定义行为的参数
var parameters = advancedGenerator.GenerateParametersWithBehavior(
    organization: "your_organization",
    captchaUuid: "your_captcha_uuid",
    mouseBehavior: mouseBehavior
);
```

## 项目结构

```
├── ShumeiCaptcha.cs          # 主要的参数生成器类
├── CaptchaParameters.cs      # 参数模型定义
├── Program.cs                # 示例程序和高级生成器
├── ShumeiCaptcha.csproj      # 项目文件
└── ShumeiCaptcha_README.md   # 项目文档
```

## 核心类说明

### ShumeiCaptchaGenerator

主要的参数生成器类，包含以下核心方法：

- `GenerateParameters()`: 生成所有验证参数
- `BuildVerifyUrl()`: 构建完整的验证URL
- `GetEncryptContent()`: getEncryptContent 加密函数
- `EncryptMainFunction()`: 5|2|4|3|0|1 加密函数

### CaptchaParameters

验证参数模型，包含所有必要的参数字段：

- 基础参数: Organization, CaptchaUuid, Protocol 等
- 加密参数: Vo, Hg, Qt, Th, Lf 等

### AdvancedCaptchaGenerator

高级参数生成器，支持自定义行为数据：

- 自定义鼠标轨迹
- 自定义键盘行为
- 更精确的参数控制

## 算法实现细节

### 加密算法

1. **getEncryptContent 算法**:
   ```
   数据 + 盐值 + 时间戳 + 会话ID → SHA256 → Base64
   ```

2. **5|2|4|3|0|1 算法**:
   ```
   数据 + 密钥 + 时间戳 + 设备ID → AES加密 → 自定义处理 → Base64
   ```

### 数据生成策略

- **设备指纹**: 16字节随机数转换为十六进制
- **会话ID**: `sm_` + 时间戳 + 6位随机数
- **请求ID**: 时间戳 + 随机十六进制数
- **鼠标轨迹**: 模拟真实的移动路径和时间间隔

## 运行要求

- .NET 6.0 或更高版本
- System.Text.Json 包

## 编译和运行

```bash
# 编译项目
dotnet build

# 运行示例
dotnet run
```

## 注意事项

1. **仅供学习研究**: 本项目仅用于技术学习和研究目的
2. **参数时效性**: 生成的参数具有时效性，需要及时使用
3. **加密密钥**: 实际使用中的加密密钥可能需要动态获取
4. **行为模拟**: 建议使用真实的用户行为数据以提高成功率

## 技术特点

- **完整实现**: 覆盖了所有主要的参数生成算法
- **模块化设计**: 各个功能模块独立，便于维护和扩展
- **类型安全**: 使用强类型定义，减少运行时错误
- **可扩展性**: 支持自定义行为数据和参数控制
- **高性能**: 使用高效的加密算法和数据处理方式

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

const fs = require("fs")
const path = require("path")
const babel = require("@babel/core")
const traverse = require("@babel/traverse").default
const t = require("@babel/types")

class CaptchaDeobfuscator {
  constructor() {
    this.stringArrays = new Map()
    this.decodeFunctions = new Map()
    this.replacements = new Map()
    this.statistics = {
      stringsRestored: 0,
      propertiesRestored: 0,
      functionsRenamed: 0,
    }
  }

  // 主要反混淆方法
  deobfuscate(code) {
    console.log("开始反混淆处理...")

    let result = code

    // 第一步：解析并提取字符串数组
    result = this.extractStringArrays(result)

    // 第二步：还原字符串引用
    result = this.restoreStringReferences(result)

    // 第三步：简化控制流
    result = this.simplifyControlFlow(result)

    // 第四步：还原属性访问
    result = this.restorePropertyAccess(result)

    // 第五步：重命名变量和函数
    result = this.renameVariables(result)

    // 第六步：格式化代码
    result = this.formatCode(result)

    this.printStatistics()
    return result
  }

  // 提取字符串数组
  extractStringArrays(code) {
    console.log("提取字符串数组...")

    const ast = babel.parseSync(code, {
      sourceType: "script",
      parserOpts: {
        strictMode: false,
        allowImportExportEverywhere: true,
        allowAwaitOutsideFunction: true,
        allowReturnOutsideFunction: true,
        allowUndeclaredExports: true,
        errorRecovery: true,
      },
    })

    const visitor = {
      FunctionDeclaration: (path) => {
        // 查找返回字符串数组的函数
        if (this.isStringArrayFunction(path.node)) {
          const functionName = path.node.id.name
          const stringArray = this.extractStringArrayFromFunction(path.node)
          if (stringArray.length > 0) {
            this.stringArrays.set(functionName, stringArray)
            console.log(
              `发现字符串数组函数: ${functionName}, 包含 ${stringArray.length} 个字符串`
            )
          }
        }

        // 查找解码函数
        if (this.isDecodeFunction(path.node)) {
          const functionName = path.node.id.name
          this.decodeFunctions.set(functionName, path.node)
          console.log(`发现解码函数: ${functionName}`)
        }
      },
    }

    traverse(ast, visitor)
    return code
  }

  // 判断是否为字符串数组函数
  isStringArrayFunction(node) {
    if (!node.body || !node.body.body) return false

    // 查找返回数组的语句
    for (const stmt of node.body.body) {
      if (t.isReturnStatement(stmt) && t.isArrayExpression(stmt.argument)) {
        return stmt.argument.elements.length > 50 // 字符串数组通常很大
      }
      if (t.isVariableDeclaration(stmt)) {
        for (const decl of stmt.declarations) {
          if (
            t.isArrayExpression(decl.init) &&
            decl.init.elements.length > 50
          ) {
            return true
          }
        }
      }
    }
    return false
  }

  // 从函数中提取字符串数组
  extractStringArrayFromFunction(node) {
    const strings = []

    const extractFromArray = (arrayExpr) => {
      if (t.isArrayExpression(arrayExpr)) {
        for (const element of arrayExpr.elements) {
          if (t.isStringLiteral(element)) {
            strings.push(element.value)
          }
        }
      }
    }

    // 遍历函数体查找数组
    for (const stmt of node.body.body) {
      if (t.isReturnStatement(stmt)) {
        extractFromArray(stmt.argument)
      }
      if (t.isVariableDeclaration(stmt)) {
        for (const decl of stmt.declarations) {
          extractFromArray(decl.init)
        }
      }
    }

    return strings
  }

  // 判断是否为解码函数
  isDecodeFunction(node) {
    if (!node.body || !node.body.body) return false

    // 解码函数通常包含数组访问和数学运算
    let hasArrayAccess = false
    let hasMathOperation = false

    traverse(t.program([node]), {
      MemberExpression: (path) => {
        if (
          t.isIdentifier(path.node.property) ||
          t.isNumericLiteral(path.node.property)
        ) {
          hasArrayAccess = true
        }
      },
      BinaryExpression: (path) => {
        if (["+", "-", "*", "/", "%"].includes(path.node.operator)) {
          hasMathOperation = true
        }
      },
    })

    return hasArrayAccess && hasMathOperation
  }

  // 还原字符串引用
  restoreStringReferences(code) {
    console.log("还原字符串引用...")

    if (this.stringArrays.size === 0) {
      console.log("未找到字符串数组，跳过字符串还原")
      return code
    }

    // 使用正则表达式匹配常见的字符串访问模式
    let result = code

    // 模式1: _0x4d68(0x1f0)
    result = result.replace(/_0x[a-f0-9]+\(0x[a-f0-9]+\)/g, (match) => {
      try {
        const hexMatch = match.match(/0x([a-f0-9]+)/)
        if (hexMatch) {
          const index = parseInt(hexMatch[1], 16)
          // 尝试从第一个字符串数组获取
          const firstArray = Array.from(this.stringArrays.values())[0]
          if (firstArray && firstArray[index]) {
            this.statistics.stringsRestored++
            return `"${firstArray[index].replace(/"/g, '\\"')}"`
          }
        }
      } catch (e) {
        // 忽略错误，保持原样
      }
      return match
    })

    // 模式2: _0x2ffd()[index]
    for (const [funcName, stringArray] of this.stringArrays) {
      const pattern = new RegExp(`${funcName}\\(\\)\\[([0-9]+)\\]`, "g")
      result = result.replace(pattern, (match, indexStr) => {
        const index = parseInt(indexStr)
        if (stringArray[index]) {
          this.statistics.stringsRestored++
          return `"${stringArray[index].replace(/"/g, '\\"')}"`
        }
        return match
      })
    }

    console.log(`已还原 ${this.statistics.stringsRestored} 个字符串`)
    return result
  }

  // 简化控制流
  simplifyControlFlow(code) {
    console.log("简化控制流...")

    // 移除复杂的三元运算符链
    let result = code.replace(/\?\s*[^:]+\s*:\s*[^,;]+/g, (match) => {
      if (match.length > 100) {
        return "true" // 简化为简单值
      }
      return match
    })

    // 简化复杂的逻辑表达式
    result = result.replace(/&&\s*!!?\[\]/g, "")
    result = result.replace(/\|\|\s*!!?\[\]/g, "")

    return result
  }

  // 还原属性访问
  restorePropertyAccess(code) {
    console.log("还原属性访问...")

    let result = code

    // 还原常见的属性访问模式
    const propertyMappings = {
      exports: "exports",
      prototype: "prototype",
      constructor: "constructor",
      length: "length",
      push: "push",
      call: "call",
      apply: "apply",
      toString: "toString",
      valueOf: "valueOf",
      hasOwnProperty: "hasOwnProperty",
    }

    for (const [encoded, decoded] of Object.entries(propertyMappings)) {
      const pattern = new RegExp(`\\['${encoded}'\\]`, "g")
      result = result.replace(pattern, `.${decoded}`)
      this.statistics.propertiesRestored++
    }

    return result
  }

  // 重命名变量和函数
  renameVariables(code) {
    console.log("重命名变量和函数...")

    let result = code
    let counter = 0

    // 重命名混淆的变量名
    result = result.replace(/_0x[a-f0-9]+/g, (match) => {
      if (!this.replacements.has(match)) {
        this.replacements.set(match, `var_${counter++}`)
        this.statistics.functionsRenamed++
      }
      return this.replacements.get(match)
    })

    return result
  }

  // 格式化代码
  formatCode(code) {
    console.log("格式化代码...")

    try {
      const result = babel.transformSync(code, {
        plugins: [],
        parserOpts: {
          sourceType: "script",
          allowImportExportEverywhere: true,
          allowAwaitOutsideFunction: true,
          allowReturnOutsideFunction: true,
          allowUndeclaredExports: true,
          strictMode: false,
          errorRecovery: true,
        },
        generatorOpts: {
          compact: false,
          minified: false,
          comments: true,
        },
      })
      return result.code
    } catch (error) {
      console.log("格式化失败，返回原始处理结果:", error.message)
      return code
    }
  }

  // 打印统计信息
  printStatistics() {
    console.log("\n=== 反混淆统计 ===")
    console.log(`字符串还原: ${this.statistics.stringsRestored}`)
    console.log(`属性还原: ${this.statistics.propertiesRestored}`)
    console.log(`变量重命名: ${this.statistics.functionsRenamed}`)
    console.log(`字符串数组: ${this.stringArrays.size}`)
    console.log(`解码函数: ${this.decodeFunctions.size}`)
  }
}

// 主函数
function main() {
  const inputFile = "captcha-sdk.min.js"
  const outputFile = "captcha-sdk.deobfuscated.js"

  if (!fs.existsSync(inputFile)) {
    console.error(`输入文件不存在: ${inputFile}`)
    process.exit(1)
  }

  console.log(`读取文件: ${inputFile}`)
  const code = fs.readFileSync(inputFile, "utf8")

  const deobfuscator = new CaptchaDeobfuscator()
  const deobfuscatedCode = deobfuscator.deobfuscate(code)

  console.log(`写入文件: ${outputFile}`)
  fs.writeFileSync(outputFile, deobfuscatedCode, "utf8")

  console.log("反混淆完成！")
}

if (require.main === module) {
  main()
}

module.exports = CaptchaDeobfuscator

# Captcha SDK 反混淆工具

这是一个专门用于反混淆 `captcha-sdk.min.js` 文件的 Node.js 工具。该工具使用 AST（抽象语法树）技术和正则表达式来还原被混淆的 JavaScript 代码。

## 功能特性

- ✅ **字符串数组提取与还原**：自动识别并提取混淆代码中的字符串数组，还原字符串引用
- ✅ **属性访问还原**：将 `['property']` 形式的属性访问还原为 `.property` 形式
- ✅ **变量重命名**：将混淆的变量名（如 `_0x4d68`）重命名为更清晰的名称
- ✅ **控制流简化**：简化复杂的三元运算符和逻辑表达式
- ✅ **函数调用还原**：还原常见的函数调用模式
- ✅ **代码格式化**：基本的代码格式化，提高可读性

## 安装依赖

```bash
npm install
```

## 使用方法

### 方法一：使用简化版反混淆工具（推荐）

```bash
node simple-deobfuscator.js
```

### 方法二：使用完整版反混淆工具

```bash
node deobfuscator.js
```

## 输入输出

- **输入文件**：`captcha-sdk.min.js`（需要放在同一目录下）
- **输出文件**：`captcha-sdk.deobfuscated.js`

## 反混淆效果

### 处理前（混淆代码）
```javascript
function _0x4d68(_0xdf00ca,_0x32d3e8){var _0x42d9a6=_0x2ffd();return _0x4d68=function(_0x3e4296,_0x4416a9){_0x3e4296=_0x3e4296-(-0x95a+0x143*0x18+-0x143b);var _0x5f0166=_0x42d9a6[_0x3e4296];return _0x5f0166;},_0x4d68(_0xdf00ca,_0x32d3e8);}
```

### 处理后（反混淆代码）
```javascript
function var_0(var_1, var_2) {
  var var_3 = var_4();
  return var_0 = function(var_5, var_6) {
    var_5 = var_5 - (0);
    var var_7 = var_3[var_5];
    return var_7;
  }, var_0(var_1, var_2);
}
```

## 统计信息示例

```
=== 反混淆统计 ===
字符串还原: 5234
属性还原: 12
变量重命名: 4701
控制流简化: 10
字符串数组: 1
```

## 技术原理

### 1. 字符串数组提取
- 识别返回大型字符串数组的函数（如 `_0x2ffd`）
- 解析数组中的字符串字面量
- 处理转义字符和 Unicode 编码

### 2. 字符串引用还原
- 匹配 `_0x4d68(0x1f0)` 形式的函数调用
- 匹配 `_0x2ffd()[index]` 形式的数组访问
- 将索引转换为对应的字符串字面量

### 3. 控制流简化
- 简化复杂的三元运算符链
- 移除冗余的逻辑表达式
- 简化数学运算表达式

### 4. 属性访问还原
- 将 `['exports']` 转换为 `.exports`
- 将 `["prototype"]` 转换为 `.prototype`
- 处理常见的 JavaScript 属性名

### 5. 变量重命名
- 收集所有混淆的变量名
- 生成有意义的替代名称
- 执行全局替换

## 文件结构

```
├── captcha-sdk.min.js          # 输入的混淆文件
├── simple-deobfuscator.js      # 简化版反混淆工具（推荐）
├── deobfuscator.js            # 完整版反混淆工具
├── package.json               # 项目配置
├── README.md                  # 使用说明
└── captcha-sdk.deobfuscated.js # 输出的反混淆文件
```

## 注意事项

1. **兼容性**：该工具专门针对 captcha-sdk.min.js 的混淆模式设计
2. **完整性**：反混淆后的代码可能仍需要手动调整
3. **性能**：处理大文件时可能需要一些时间
4. **准确性**：某些复杂的混淆模式可能无法完全还原

## 扩展功能

如需添加更多反混淆功能，可以修改以下方法：

- `extractStringArrays()` - 字符串数组提取逻辑
- `restoreStringReferences()` - 字符串引用还原逻辑
- `simplifyControlFlow()` - 控制流简化逻辑
- `restorePropertyAccess()` - 属性访问还原逻辑
- `renameVariables()` - 变量重命名逻辑

## 许可证

MIT License

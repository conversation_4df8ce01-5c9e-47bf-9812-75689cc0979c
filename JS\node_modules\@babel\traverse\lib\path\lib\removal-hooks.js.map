{"version": 3, "names": ["hooks", "exports", "self", "parent", "removeParent", "key", "<PERSON><PERSON><PERSON><PERSON>", "isSwitchCase", "isExportDeclaration", "isLabeledStatement", "<PERSON><PERSON><PERSON>", "isVariableDeclaration", "node", "declarations", "length", "isExpressionStatement", "remove", "isSequenceExpression", "expressions", "replaceWith", "isBinary", "right", "left", "isIfStatement", "isLoop", "isArrowFunctionExpression", "type", "body"], "sources": ["../../../src/path/lib/removal-hooks.ts"], "sourcesContent": ["// this file contains hooks that handle ancestry cleanup of parent nodes when removing children\n\nimport type Node<PERSON>ath from \"../index.ts\";\nimport type * as t from \"@babel/types\";\n/**\n * Pre hooks should be used for either rejecting removal or delegating removal\n */\n\nexport const hooks = [\n  function (self: NodePath, parent: NodePath) {\n    const removeParent =\n      // while (NODE);\n      // removing the test of a while/switch, we can either just remove it entirely *or* turn the\n      // `test` into `true` unlikely that the latter will ever be what's wanted so we just remove\n      // the loop to avoid infinite recursion\n      (self.key === \"test\" && (parent.isWhile() || parent.isSwitchCase())) ||\n      // export NODE;\n      // just remove a declaration for an export as this is no longer valid\n      (self.key === \"declaration\" && parent.isExportDeclaration()) ||\n      // label: NODE\n      // stray labeled statement with no body\n      (self.key === \"body\" && parent.isLabeledStatement()) ||\n      // let NODE;\n      // remove an entire declaration if there are no declarators left\n      (self.listKey === \"declarations\" &&\n        parent.isVariableDeclaration() &&\n        parent.node.declarations.length === 1) ||\n      // NODE;\n      // remove the entire expression statement if there's no expression\n      (self.key === \"expression\" && parent.isExpressionStatement());\n\n    if (removeParent) {\n      parent.remove();\n      return true;\n    }\n  },\n\n  function (self: NodePath, parent: NodePath) {\n    if (parent.isSequenceExpression() && parent.node.expressions.length === 1) {\n      // (node, NODE);\n      // we've just removed the second element of a sequence expression so let's turn that sequence\n      // expression into a regular expression\n      parent.replaceWith(parent.node.expressions[0]);\n      return true;\n    }\n  },\n\n  function (self: NodePath, parent: NodePath) {\n    if (parent.isBinary()) {\n      // left + NODE;\n      // NODE + right;\n      // we're in a binary expression, better remove it and replace it with the last expression\n      if (self.key === \"left\") {\n        parent.replaceWith(parent.node.right);\n      } else {\n        // key === \"right\"\n        parent.replaceWith(parent.node.left);\n      }\n      return true;\n    }\n  },\n\n  function (self: NodePath, parent: NodePath) {\n    if (\n      (parent.isIfStatement() && self.key === \"consequent\") ||\n      (self.key === \"body\" &&\n        (parent.isLoop() || parent.isArrowFunctionExpression()))\n    ) {\n      self.replaceWith({\n        type: \"BlockStatement\",\n        body: [],\n      } as t.BlockStatement);\n      return true;\n    }\n  },\n];\n"], "mappings": ";;;;;;AAQO,MAAMA,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAG,CACnB,UAAUE,IAAc,EAAEC,MAAgB,EAAE;EAC1C,MAAMC,YAAY,GAKfF,IAAI,CAACG,GAAG,KAAK,MAAM,KAAKF,MAAM,CAACG,OAAO,CAAC,CAAC,IAAIH,MAAM,CAACI,YAAY,CAAC,CAAC,CAAC,IAGlEL,IAAI,CAACG,GAAG,KAAK,aAAa,IAAIF,MAAM,CAACK,mBAAmB,CAAC,CAAE,IAG3DN,IAAI,CAACG,GAAG,KAAK,MAAM,IAAIF,MAAM,CAACM,kBAAkB,CAAC,CAAE,IAGnDP,IAAI,CAACQ,OAAO,KAAK,cAAc,IAC9BP,MAAM,CAACQ,qBAAqB,CAAC,CAAC,IAC9BR,MAAM,CAACS,IAAI,CAACC,YAAY,CAACC,MAAM,KAAK,CAAE,IAGvCZ,IAAI,CAACG,GAAG,KAAK,YAAY,IAAIF,MAAM,CAACY,qBAAqB,CAAC,CAAE;EAE/D,IAAIX,YAAY,EAAE;IAChBD,MAAM,CAACa,MAAM,CAAC,CAAC;IACf,OAAO,IAAI;EACb;AACF,CAAC,EAED,UAAUd,IAAc,EAAEC,MAAgB,EAAE;EAC1C,IAAIA,MAAM,CAACc,oBAAoB,CAAC,CAAC,IAAId,MAAM,CAACS,IAAI,CAACM,WAAW,CAACJ,MAAM,KAAK,CAAC,EAAE;IAIzEX,MAAM,CAACgB,WAAW,CAAChB,MAAM,CAACS,IAAI,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,IAAI;EACb;AACF,CAAC,EAED,UAAUhB,IAAc,EAAEC,MAAgB,EAAE;EAC1C,IAAIA,MAAM,CAACiB,QAAQ,CAAC,CAAC,EAAE;IAIrB,IAAIlB,IAAI,CAACG,GAAG,KAAK,MAAM,EAAE;MACvBF,MAAM,CAACgB,WAAW,CAAChB,MAAM,CAACS,IAAI,CAACS,KAAK,CAAC;IACvC,CAAC,MAAM;MAELlB,MAAM,CAACgB,WAAW,CAAChB,MAAM,CAACS,IAAI,CAACU,IAAI,CAAC;IACtC;IACA,OAAO,IAAI;EACb;AACF,CAAC,EAED,UAAUpB,IAAc,EAAEC,MAAgB,EAAE;EAC1C,IACGA,MAAM,CAACoB,aAAa,CAAC,CAAC,IAAIrB,IAAI,CAACG,GAAG,KAAK,YAAY,IACnDH,IAAI,CAACG,GAAG,KAAK,MAAM,KACjBF,MAAM,CAACqB,MAAM,CAAC,CAAC,IAAIrB,MAAM,CAACsB,yBAAyB,CAAC,CAAC,CAAE,EAC1D;IACAvB,IAAI,CAACiB,WAAW,CAAC;MACfO,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE;IACR,CAAqB,CAAC;IACtB,OAAO,IAAI;EACb;AACF,CAAC,CACF", "ignoreList": []}
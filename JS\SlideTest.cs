using System;
using System.Threading.Tasks;
using ShumeiCaptcha;

namespace SlideTest
{
    class Program
    {
        static async Task Mai1n(string[] args)
        {
            Console.WriteLine("数美滑动验证码修复测试");
            Console.WriteLine("======================");

            try
            {
                // 测试参数生成
                var generator = new ShumeiCaptchaGenerator();
                
                // 模拟滑动轨迹数据
                var trajectories = new KeyValuePair<string, int>(
                    "[[0,10,0],[5,12,50],[15,14,120],[30,16,200],[50,18,300],[80,20,420],[120,22,580],[160,24,750],[200,26,920],[240,28,1100]]",
                    1100
                );
                
                var organization = "d6tpAY1oV0Kv5jRSgxQr";
                var captchaUuid = DateTime.Now.ToString("yyyyMMddHHmmss") + "sNyZxdHW6ER3Cepw2x";
                var rid = DateTime.Now.ToString("yyyyMMddHHmmss") + "990fd223cf78e204a3";
                var slideDistance = 240;
                
                Console.WriteLine("生成滑动验证码参数...");
                var parameters = generator.GenerateSlideParameters(organization, captchaUuid, rid, trajectories, slideDistance);
                
                Console.WriteLine("✅ 参数生成成功!");
                Console.WriteLine($"Organization: {parameters.Organization}");
                Console.WriteLine($"CaptchaUuid: {parameters.CaptchaUuid}");
                Console.WriteLine($"Protocol: {parameters.Protocol}");
                Console.WriteLine($"Rid: {parameters.Rid}");
                Console.WriteLine();
                
                Console.WriteLine("加密参数预览:");
                Console.WriteLine($"Vo (鼠标数据): {parameters.Vo.Substring(0, Math.Min(20, parameters.Vo.Length))}...");
                Console.WriteLine($"Hg (硬件指纹): {parameters.Hg.Substring(0, Math.Min(20, parameters.Hg.Length))}...");
                Console.WriteLine($"Qt (时间戳): {parameters.Qt.Substring(0, Math.Min(20, parameters.Qt.Length))}...");
                Console.WriteLine($"Th (轨迹数据): {parameters.Th.Substring(0, Math.Min(20, parameters.Th.Length))}...");
                Console.WriteLine($"Lf (屏幕信息): {parameters.Lf.Substring(0, Math.Min(20, parameters.Lf.Length))}...");
                Console.WriteLine();
                
                // 构建URL
                Console.WriteLine("构建验证URL...");
                var verifyUrl = generator.BuildVerifyUrl(parameters);
                
                Console.WriteLine("✅ URL构建成功!");
                Console.WriteLine($"URL长度: {verifyUrl.Length} 字符");
                Console.WriteLine($"URL预览: {verifyUrl.Substring(0, Math.Min(100, verifyUrl.Length))}...");
                Console.WriteLine();
                
                // 对比真实URL格式
                Console.WriteLine("📊 与真实URL格式对比:");
                Console.WriteLine("真实URL包含的参数:");
                var realParams = new[] { "lf", "act.os", "ny", "bs", "sdkver", "to", "hg", "ostype", "qt", "gg", "yh", "captchaUuid", "rversion", "protocol", "organization", "th", "rid", "fm", "bq", "sl", "callback" };
                
                foreach (var param in realParams)
                {
                    bool hasParam = verifyUrl.Contains($"{param}=");
                    Console.WriteLine($"  {param}: {(hasParam ? "✅" : "❌")}");
                }
                
                Console.WriteLine();
                Console.WriteLine("🎯 关键发现:");
                Console.WriteLine("1. 新的滑动验证码不再使用 tb, tm, ly 等DES加密参数");
                Console.WriteLine("2. 完全采用新的参数生成算法 (getEncryptContent + 5|2|4|3|0|1)");
                Console.WriteLine("3. 协议版本使用 185 而不是 184");
                Console.WriteLine("4. 参数格式与通用验证码保持一致");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

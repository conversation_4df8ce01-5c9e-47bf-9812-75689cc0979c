﻿using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Shumei.Services
{
    public static class ShuMeiDevice
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="organization"></param>
        /// <param name="appId"></param>
        /// <param name="publicKey"></param>
        /// <returns></returns>
        public static async Task<string> DeviceProfile(string organization, string appId, string publicKey)
        {
            try
            {

                //    var json = new Dictionary<string, object>
                //{
                //    { "networkType", "wifi" },
                //    { "brand", "microsoft" },
                //    { "model", "microsoft" },
                //    { "notificationSoundEnabled", true },
                //    { "platform", "windows" },
                //    { "power", 100 },
                //    { "version", "3.0.0" },
                //    { "SDKVersion", "3.3.5" },
                //    { "devicePixelRatio", 1 },
                //    { "canvas", GetRandomString(30).Substring(0, 8) },
                //    { "CPUType", "Not Available" },
                //    { "memorySize", Random.Shared.Next(30509, 35509) },
                //    { "nfcErrorMsg", "startDiscovery:fail current platform is not supported" },
                //    { "organization", organization },
                //    { "appId", appId },
                //    { "os", "weapp" },
                //    { "sdkVer", "1.0.0" },
                //    { "rtype", "all" },
                //    { "smid", GetLocalSmid() },
                //    { "box", "" },
                //    { "channel", "" },
                //    { "subVersion", "1.0.0" },
                //    { "weAppVerion", "3.9.10" },
                //    { "launchOptions.path", "pages/home/<USER>" },
                //    { "launchOptions.query","" },
                //    { "launchOptions.scene", 1256 },
                //    { "launchOptions.referrerInfo","" },
                //    { "launchOptions.apiCategory", "default" }
                //};



                var json = new Dictionary<string, object>
                {
                    { "networkType", "wifi" },
                    { "batteryLevel", 100 },
                    { "brand", "microsoft" },
                    { "memorySize", 2048 },
                    { "model", "iPhone 12/13 (Pro)" },
                    { "platform", "windows" },
                    { "screenTop", 82 },
                    { "version", "3.0.0" },
                    { "SDKVersion", "3.8.2" },
                    { "fontSizeScaleFactor", 1 },
                    { "mode", "default" },
                    { "bluetoothAuthorized", true },
                    { "devicePixelRatio", 3 },
                    { "canvas", GetRandomString(8) },
                    { "nfcErrorMsg", "startDiscovery:fail current platform is not supported" },
                    { "organization",organization},
                    { "appId", appId },
                    { "os", "weapp" },
                    { "sdkVer", "1.0.0" },
                    { "rtype", "all" },
                    { "smid",  GetLocalSmid()},
                    { "box", "" }, //sQ9hbfZRtTr2CgL9j/Bdx/Ll1JYMeoTMQ2Y+IYqi/Qg9nV77U+oY52HsfGlR+Y86sJ0nLQP1VXTmHHQM41mceA==
                    { "channel", "" },
                    { "subVersion", "1.0.0" },
                    { "weAppVerion", "8.0.5" },
                    { "launchOptions", new Dictionary<string, object>
                        {
                            { "path", "pages/home/<USER>" },
                            { "query", null }, // 空字典表示空对象 
                            { "scene", 1001 },
                            { "referrerInfo", null }, // 空字典表示空对象 
                            { "mode", "default" },
                            { "apiCategory", "default" }
                        }
                    },
                };

                string sortedJson = HashMapSort(json);
                json["tn"] = ComputeHash(sortedJson);

                string data = Deflate(JsonConvert.SerializeObject(json));
                string uid = GetUid();
                string priId = ComputeHash(uid).Substring(0, 16);

                RSA rsa = LoadPublicKeyFromBase64(publicKey);
                byte[] encryptedData = EncryptData(rsa, uid);
                string ep = Convert.ToBase64String(encryptedData);
                data = AesEncrypt(data, priId);

                var requestJson = new Dictionary<string, object>
                {
                    { "appId", appId },
                    { "compress", 2 },
                    { "data", data },
                    { "encode", 6 },
                    { "ep", ep },
                    { "organization", organization },
                    { "os", "weapp" }
                };

                string requestData = JsonConvert.SerializeObject(requestJson);
                var proxy = await ProxyUtils.GetProxyIp();
                using HttpClient http = new HttpClient(new HttpClientHandler { UseProxy = true, Proxy = proxy });
                var resp = await http.PostAsync("https://fp-it.fengkongcloud.com/deviceprofile/v4", new StringContent(requestData, Encoding.UTF8, "application/json"));
                var result = await resp.Content.ReadAsStringAsync();
                if (result.Contains("\"deviceId\":"))
                {
                    var jsonObject = JObject.Parse(result);
                    var deviceId = jsonObject["detail"]?["deviceId"]?.ToString();
                    return $"B{deviceId}";
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return string.Empty;
        }

        private static RSA LoadPublicKeyFromBase64(string base64PublicKey)
        {
            // 解码Base64公钥
            byte[] publicKeyBytes = Convert.FromBase64String(base64PublicKey);

            // 校验公钥长度是否符合预期
            if (publicKeyBytes.Length < 162)
            {
                throw new ArgumentException("Invalid public key length.");
            }

            // 提取模数和指数的位置和长度
            const int modulusStartIndex = 29;
            const int modulusLength = 128;
            const int exponentStartIndex = 159;
            const int exponentLength = 3;

            // 提取模数和指数
            byte[] modulus = new byte[modulusLength];
            byte[] exponent = new byte[exponentLength];
            Array.Copy(publicKeyBytes, modulusStartIndex, modulus, 0, modulusLength);
            Array.Copy(publicKeyBytes, exponentStartIndex, exponent, 0, exponentLength);

            // 创建RSA实例并导入参数
            RSA rsa = RSA.Create();
            RSAParameters rsaParams = new RSAParameters
            {
                Modulus = modulus,
                Exponent = exponent
            };
            rsa.ImportParameters(rsaParams);

            return rsa;
        }

        // 示例：使用公钥加密数据 
        private static byte[] EncryptData(RSA rsa, string plainText)
        {
            byte[] dataToEncrypt = Encoding.UTF8.GetBytes(plainText);
            return rsa.Encrypt(dataToEncrypt, RSAEncryptionPadding.Pkcs1);
        }

        private static string GetUid()
        {
            return Regex.Replace("xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx", "[xy]", match =>
            {
                var random = new Random();
                int e = random.Next(0, 16);
                char c = match.Value[0];
                return (c == 'x' ? e : (e & 0x3 | 0x8)).ToString("x");
            });
        }
        private static string GetLocalSmid()
        {
            DateTime now = DateTime.Now;
            string uuid = GetUid();
            string a = $"{now:yyyyMMddHHmmss}{ComputeHash(uuid)}00";
            return $"{a}{ComputeHash($"smsk_weapp_{a}").Substring(0, 14)}0";

        }


        private static string ComputeHash(string input)
        {
            using var md5 = MD5.Create();
            byte[] hash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
            return BitConverter.ToString(hash).Replace("-", "").ToLower();
        }

        private static string AesEncrypt(string data, string key)
        {
            using var aes = Aes.Create();
            aes.Key = Encoding.UTF8.GetBytes(key);
            aes.IV = Encoding.UTF8.GetBytes("0102030405060708");
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.Zeros;

            using var encryptor = aes.CreateEncryptor();
            byte[] inputBytes = Encoding.UTF8.GetBytes(data);
            byte[] encryptedBytes = encryptor.TransformFinalBlock(inputBytes, 0, inputBytes.Length);
            return BitConverter.ToString(encryptedBytes).Replace("-", "").ToLower();
        }

        private static string Deflate(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                throw new ArgumentNullException(nameof(text), "Input text cannot be null or empty.");
            }

            byte[] inputBytes = Encoding.UTF8.GetBytes(text);
            using var outputStream = new MemoryStream();
            using (var gzipStream = new GZipStream(outputStream, CompressionMode.Compress))
            {
                gzipStream.Write(inputBytes, 0, inputBytes.Length);
            }
            return Convert.ToBase64String(outputStream.ToArray());
        }

        private static string HashMapSort(Dictionary<string, object> json)
        {
            var sortedKeys = json.Keys.OrderBy(k => k).ToList();
            var sortedJson = new StringBuilder();

            foreach (var key in sortedKeys)
            {
                if (json[key] is Dictionary<string, object> dict)
                {
                    foreach (var o in dict)
                        sortedJson.Append(o.Value);
                }
                else
                    sortedJson.Append(json[key]);
            }

            return sortedJson.ToString();
        }



        private static string GetRandomString(int length)
        {
            const string chars = "abcdef0123456789";
            return new string(Enumerable.Repeat(chars, length).Select(s => s[Random.Shared.Next(s.Length)]).ToArray());
        }
    }
}
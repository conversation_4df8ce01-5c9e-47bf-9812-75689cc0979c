{"version": 3, "names": ["_defineProperty", "require", "_objectSpread", "target", "i", "arguments", "length", "source", "Object", "ownKeys", "keys", "getOwnPropertySymbols", "push", "apply", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "for<PERSON>ach", "key", "defineProperty"], "sources": ["../../src/helpers/objectSpread.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n/* @onlyBabel7 */\n\nimport defineProperty from \"./defineProperty.ts\";\n\ntype Intersection<R extends any[]> = R extends [infer H, ...infer S]\n  ? H & Intersection<S>\n  : unknown;\n\nexport default function _objectSpread<T extends object, U extends unknown[]>(\n  target: T,\n  ...sources: U\n): T & Intersection<U>;\nexport default function _objectSpread(target: object) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source: object = arguments[i] != null ? Object(arguments[i]) : {};\n    var ownKeys: PropertyKey[] = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === \"function\") {\n      ownKeys.push.apply(\n        ownKeys,\n        Object.getOwnPropertySymbols(source).filter(function (sym) {\n          // sym already comes from `Object.getOwnPropertySymbols`, so getOwnPropertyDescriptor should always be defined\n          return Object.getOwnPropertyDescriptor(source, sym)!.enumerable;\n        }),\n      );\n    }\n    ownKeys.forEach(function (key) {\n      defineProperty(target, key, source[key as keyof typeof source]);\n    });\n  }\n  return target;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,eAAA,GAAAC,OAAA;AAUe,SAASC,aAAaA,CAACC,MAAc,EAAE;EACpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAc,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGI,MAAM,CAACH,SAAS,CAACD,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACrE,IAAIK,OAAsB,GAAGD,MAAM,CAACE,IAAI,CAACH,MAAM,CAAC;IAChD,IAAI,OAAOC,MAAM,CAACG,qBAAqB,KAAK,UAAU,EAAE;MACtDF,OAAO,CAACG,IAAI,CAACC,KAAK,CAChBJ,OAAO,EACPD,MAAM,CAACG,qBAAqB,CAACJ,MAAM,CAAC,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAE;QAEzD,OAAOP,MAAM,CAACQ,wBAAwB,CAACT,MAAM,EAAEQ,GAAG,CAAC,CAAEE,UAAU;MACjE,CAAC,CACH,CAAC;IACH;IACAR,OAAO,CAACS,OAAO,CAAC,UAAUC,GAAG,EAAE;MAC7B,IAAAC,uBAAc,EAACjB,MAAM,EAAEgB,GAAG,EAAEZ,MAAM,CAACY,GAAG,CAAwB,CAAC;IACjE,CAAC,CAAC;EACJ;EACA,OAAOhB,MAAM;AACf", "ignoreList": []}
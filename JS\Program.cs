using Shumei.Services;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace ShumeiCaptcha
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("数美验证码参数生成器 - C# 实现");
            Console.WriteLine("=====================================");

           await ShuMeiSlide.Recognize("qvdTr7A9k1DIU04ha4eP");

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 发送验证请求
        /// </summary>
        static async Task<CaptchaResponse> SendVerifyRequest(string url)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Add("User-Agent", 
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                var response = await httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();

                Console.WriteLine($"HTTP状态码: {response.StatusCode}");
                Console.WriteLine($"响应内容: {content}");

                // 解析JSONP响应
                if (content.Contains("(") && content.Contains(")"))
                {
                    var startIndex = content.IndexOf('(') + 1;
                    var endIndex = content.LastIndexOf(')');
                    var jsonContent = content.Substring(startIndex, endIndex - startIndex);

                    var captchaResponse = JsonSerializer.Deserialize<CaptchaResponse>(jsonContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return captchaResponse;
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"请求异常: {ex.Message}");
                return null;
            }
        }
    }

    /// <summary>
    /// 高级参数生成器 - 提供更精确的参数控制
    /// </summary>
    public class AdvancedCaptchaGenerator : ShumeiCaptchaGenerator
    {
        /// <summary>
        /// 生成带有自定义行为数据的参数
        /// </summary>
        public CaptchaParameters GenerateParametersWithBehavior(
            string organization,
            string captchaUuid,
            MouseBehaviorData mouseBehavior = null,
            KeyboardBehaviorData keyboardBehavior = null)
        {
            var parameters = GenerateParameters(organization, captchaUuid);

            // 如果提供了自定义行为数据，重新生成相关参数
            if (mouseBehavior != null)
            {
                var mouseJson = JsonSerializer.Serialize(mouseBehavior);
                parameters.Vo = GetEncryptContent(mouseJson, "2|9|3|13|1|8|11|7|14|4|6|5|10|12|0");
                parameters.Th = EncryptMainFunction($"mouse_track_{mouseJson}", "rYhPo");
            }

            if (keyboardBehavior != null)
            {
                var keyboardJson = JsonSerializer.Serialize(keyboardBehavior);
                parameters.Bq = GetEncryptContent(keyboardJson, "bq_salt");
            }

            return parameters;
        }
    }

    /// <summary>
    /// 鼠标行为数据
    /// </summary>
    public class MouseBehaviorData
    {
        public List<MousePoint> Points { get; set; } = new List<MousePoint>();
        public long StartTime { get; set; }
        public long EndTime { get; set; }
    }

    /// <summary>
    /// 鼠标点位
    /// </summary>
    public class MousePoint
    {
        public int X { get; set; }
        public int Y { get; set; }
        public long Timestamp { get; set; }
        public string Type { get; set; } // move, click, down, up
    }

    /// <summary>
    /// 键盘行为数据
    /// </summary>
    public class KeyboardBehaviorData
    {
        public List<KeyEvent> Events { get; set; } = new List<KeyEvent>();
        public double TypingSpeed { get; set; }
        public List<long> KeyIntervals { get; set; } = new List<long>();
    }

    /// <summary>
    /// 键盘事件
    /// </summary>
    public class KeyEvent
    {
        public string Key { get; set; }
        public long Timestamp { get; set; }
        public string Type { get; set; } // keydown, keyup, keypress
        public long Duration { get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace ShumeiCaptcha
{
    /// <summary>
    /// 数美验证码参数生成器
    /// 基于对captcha-sdk.min.js的逆向分析实现
    /// </summary>
    public class ShumeiCaptchaGenerator
    {
        private readonly Random _random;
        private readonly Dictionary<string, string> _stringArray;
        private string _deviceId;
        private string _sessionId;
        private long _startTime;
        
        public ShumeiCaptchaGenerator()
        {
            _random = new Random();
            _stringArray = InitializeStringArray();
            _deviceId = GenerateDeviceId();
            _sessionId = GenerateSessionId();
            _startTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }

        /// <summary>
        /// 初始化字符串数组（模拟JavaScript中的字符串数组）
        /// </summary>
        private Dictionary<string, string> InitializeStringArray()
        {
            return new Dictionary<string, string>
            {
                ["getEncryptContent"] = "getEncryptContent",
                ["5|2|4|3|0|1"] = "5|2|4|3|0|1",
                ["makeURL"] = "makeURL",
                ["deviceId"] = "deviceId",
                ["sessionId"] = "sessionId",
                ["timestamp"] = "timestamp",
                ["mouseData"] = "mouseData",
                ["keyboardData"] = "keyboardData",
                ["screenInfo"] = "screenInfo",
                ["browserInfo"] = "browserInfo",
                ["hardwareInfo"] = "hardwareInfo"
            };
        }

        /// <summary>
        /// 生成设备ID
        /// </summary>
        private string GenerateDeviceId()
        {
            var bytes = new byte[16];
            _random.NextBytes(bytes);
            return Convert.ToHexString(bytes).ToLower();
        }

        /// <summary>
        /// 生成会话ID
        /// </summary>
        private string GenerateSessionId()
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var randomPart = _random.Next(100000, 999999);
            return $"sm_{timestamp}{randomPart}";
        }

        /// <summary>
        /// 主要的加密函数 - 对应JavaScript中的"5|2|4|3|0|1"函数
        /// </summary>
        protected string EncryptMainFunction(string data, string key)
        {
            try
            {
                // 模拟JavaScript中的加密逻辑
                var combinedData = CombineDataWithKey(data, key);
                var encrypted = PerformEncryption(combinedData);
                var processed = ProcessEncryptedData(encrypted);
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(processed));
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 辅助加密函数 - 对应JavaScript中的getEncryptContent函数
        /// </summary>
        protected string GetEncryptContent(string data, string salt)
        {
            try
            {
                // 模拟getEncryptContent的加密逻辑
                var saltedData = ApplySalt(data, salt);
                var hashed = ComputeHash(saltedData);
                return Convert.ToBase64String(hashed);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"内容加密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 数据与密钥结合
        /// </summary>
        private string CombineDataWithKey(string data, string key)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var combined = $"{data}|{key}|{timestamp}|{_deviceId}";
            return combined;
        }

        /// <summary>
        /// 执行加密操作
        /// </summary>
        private byte[] PerformEncryption(string data)
        {
            using (var aes = Aes.Create())
            {
                // 使用固定的密钥和IV（在实际应用中应该动态生成）
                var key = Encoding.UTF8.GetBytes("shumei_captcha_key_32_characters");
                var iv = Encoding.UTF8.GetBytes("shumei_iv_16char");
                
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                using (var encryptor = aes.CreateEncryptor())
                {
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    return encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length);
                }
            }
        }

        /// <summary>
        /// 处理加密后的数据
        /// </summary>
        private string ProcessEncryptedData(byte[] encryptedData)
        {
            // 模拟JavaScript中的后处理逻辑
            var processed = new StringBuilder();
            for (int i = 0; i < encryptedData.Length; i++)
            {
                processed.Append((char)(encryptedData[i] ^ (i % 256)));
            }
            return processed.ToString();
        }

        /// <summary>
        /// 应用盐值
        /// </summary>
        private string ApplySalt(string data, string salt)
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            return $"{salt}{data}{timestamp}{_sessionId}";
        }

        /// <summary>
        /// 计算哈希值
        /// </summary>
        private byte[] ComputeHash(string data)
        {
            using (var sha256 = SHA256.Create())
            {
                var dataBytes = Encoding.UTF8.GetBytes(data);
                return sha256.ComputeHash(dataBytes);
            }
        }

        /// <summary>
        /// 生成鼠标轨迹数据
        /// </summary>
        private string GenerateMouseTrackData()
        {
            var mouseData = new List<object>();
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            // 模拟鼠标移动轨迹
            for (int i = 0; i < 10; i++)
            {
                mouseData.Add(new
                {
                    x = _random.Next(0, 1920),
                    y = _random.Next(0, 1080),
                    t = currentTime + i * 100
                });
            }

            return JsonSerializer.Serialize(mouseData);
        }

        /// <summary>
        /// 生成屏幕信息
        /// </summary>
        private string GenerateScreenInfo()
        {
            var screenInfo = new
            {
                width = 1920,
                height = 1080,
                colorDepth = 24,
                pixelDepth = 24,
                availWidth = 1920,
                availHeight = 1040
            };

            return JsonSerializer.Serialize(screenInfo);
        }

        /// <summary>
        /// 生成浏览器信息
        /// </summary>
        private string GenerateBrowserInfo()
        {
            var browserInfo = new
            {
                userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                language = "zh-CN",
                platform = "Win32",
                cookieEnabled = true,
                doNotTrack = (string)null,
                timezone = -480
            };

            return JsonSerializer.Serialize(browserInfo);
        }

        /// <summary>
        /// 生成硬件指纹信息
        /// </summary>
        private string GenerateHardwareFingerprint()
        {
            var hardwareInfo = new
            {
                canvas = GenerateCanvasFingerprint(),
                webgl = GenerateWebGLFingerprint(),
                audio = GenerateAudioFingerprint(),
                fonts = GenerateFontList()
            };

            return JsonSerializer.Serialize(hardwareInfo);
        }

        /// <summary>
        /// 生成Canvas指纹
        /// </summary>
        private string GenerateCanvasFingerprint()
        {
            // 模拟Canvas指纹生成
            var canvasData = $"canvas_{_deviceId}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
            using (var md5 = MD5.Create())
            {
                var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(canvasData));
                return Convert.ToHexString(hash).ToLower();
            }
        }

        /// <summary>
        /// 生成WebGL指纹
        /// </summary>
        private string GenerateWebGLFingerprint()
        {
            return "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)";
        }

        /// <summary>
        /// 生成音频指纹
        /// </summary>
        private string GenerateAudioFingerprint()
        {
            var audioData = $"audio_{_random.NextDouble()}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
            using (var sha1 = SHA1.Create())
            {
                var hash = sha1.ComputeHash(Encoding.UTF8.GetBytes(audioData));
                return Convert.ToBase64String(hash);
            }
        }

        /// <summary>
        /// 生成字体列表
        /// </summary>
        private List<string> GenerateFontList()
        {
            return new List<string>
            {
                "Arial", "Helvetica", "Times New Roman", "Courier New", "Verdana",
                "Georgia", "Palatino", "Garamond", "Bookman", "Comic Sans MS",
                "Trebuchet MS", "Arial Black", "Impact", "Microsoft YaHei",
                "SimSun", "SimHei", "KaiTi", "FangSong"
            };
        }

        /// <summary>
        /// 生成所有验证参数
        /// </summary>
        public CaptchaParameters GenerateParameters(string organization, string captchaUuid)
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var mouseData = GenerateMouseTrackData();
            var screenInfo = GenerateScreenInfo();
            var browserInfo = GenerateBrowserInfo();
            var hardwareInfo = GenerateHardwareFingerprint();

            // 生成各个参数
            var parameters = new CaptchaParameters
            {
                Organization = organization,
                CaptchaUuid = captchaUuid,
                Protocol = "185",
                OsType = "web",
                ActOs = "web_pc",
                RVersion = "1.0.4",
                SdkVer = "1.1.3",
                Callback = _sessionId,
                Rid = GenerateRid(),

                // 使用getEncryptContent加密的参数
                Vo = GetEncryptContent(mouseData, "2|9|3|13|1|8|11|7|14|4|6|5|10|12|0"),
                Hg = GetEncryptContent(hardwareInfo, "cqhjO"),
                Qt = GetEncryptContent(currentTime.ToString(), "use strict"),

                // 使用5|2|4|3|0|1加密的参数
                Th = EncryptMainFunction($"floor_{currentTime}", "rYhPo"),
                Lf = EncryptMainFunction(screenInfo, "Di-wasto ang mga param"),

                // 其他加密参数
                Ny = GetEncryptContent(browserInfo, "ny_salt"),
                Gg = GetEncryptContent(_deviceId, "gg_salt"),
                Bs = EncryptMainFunction("browser_signature", "bs_salt"),
                Sl = GetEncryptContent("screen_lock", "sl_salt"),
                Fm = EncryptMainFunction("form_data", "fm_salt"),
                Bq = GetEncryptContent("behavior_queue", "bq_salt"),
                To = EncryptMainFunction("touch_operations", "to_salt"),
                Yh = GetEncryptContent("yh_data", "yh_salt")
            };

            return parameters;
        }

        /// <summary>
        /// 生成请求ID
        /// </summary>
        private string GenerateRid()
        {
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            var randomHex = _random.Next(0x1000000, 0xFFFFFFF).ToString("x");
            return $"{timestamp}{randomHex}";
        }

        /// <summary>
        /// 生成滑动验证码专用参数
        /// </summary>
        public CaptchaParameters GenerateSlideParameters(string organization, string captchaUuid, string rid, KeyValuePair<string, int> trajectories, int slideDistance)
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var sessionId = GenerateSessionId();

            // 生成滑动验证码特有的行为数据
            var slideMouseData = GenerateSlideMouseData(trajectories, slideDistance);
            var slideKeyboardData = GenerateSlideKeyboardData();
            var slideBehaviorData = GenerateSlideBehaviorData(trajectories, currentTime);

            // 设备和环境信息
            var hardwareInfo = GenerateHardwareFingerprint();
            var screenInfo = GenerateScreenInfo();
            var browserInfo = GenerateBrowserInfo();
            var deviceFingerprint = GenerateCanvasFingerprint() + "|" + GenerateWebGLFingerprint();
            var touchData = GenerateTouchOperations();
            var formData = GenerateFormDataInfo();
            var behaviorQueue = GenerateBehaviorQueueData();
            var yhData = GenerateYhDataInfo();
            var screenLock = GenerateScreenLockData();

            return new CaptchaParameters
            {
                Organization = organization,
                CaptchaUuid = captchaUuid,
                Protocol = "185", // 滑动验证码使用185协议
                OsType = "web",
                Rid = rid,
                Callback = $"sm_{currentTime}",

                // 使用getEncryptContent加密的参数
                Vo = GetEncryptContent(slideMouseData, "2|9|3|13|1|8|11|7|14|4|6|5|10|12|0"),
                Hg = GetEncryptContent(hardwareInfo, "cqhjO"),
                Qt = GetEncryptContent(currentTime.ToString(), "use strict"),
                Ny = GetEncryptContent(browserInfo, "browser_info"),
                Gg = GetEncryptContent(deviceFingerprint, "device_fp"),
                Sl = GetEncryptContent(screenLock, "screen_lock"),
                Bq = GetEncryptContent(behaviorQueue, "behavior_queue"),
                Yh = GetEncryptContent(yhData, "yh_data"),

                // 使用5|2|4|3|0|1加密的参数
                Th = EncryptMainFunction($"slide_track_{currentTime}", "rYhPo"),
                Lf = EncryptMainFunction(screenInfo, "Di-wasto ang mga param"),
                Bs = EncryptMainFunction(browserInfo + "_signature", "browser_sig"),
                Fm = EncryptMainFunction(formData, "form_data"),
                To = EncryptMainFunction(touchData, "touch_ops"),

                // 版本信息
                RVersion = "1.0.4",
                SdkVer = "1.1.3",
                ActOs = "web_pc"
            };
        }

        /// <summary>
        /// 生成滑动验证码特有的鼠标数据
        /// </summary>
        private string GenerateSlideMouseData(KeyValuePair<string, int> trajectories, int slideDistance)
        {
            var slideData = new
            {
                trajectory = trajectories.Key,
                duration = trajectories.Value,
                distance = slideDistance,
                startTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - trajectories.Value,
                endTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                accuracy = Math.Round(1.0 - Math.Abs(slideDistance - slideDistance) / (double)slideDistance, 4)
            };

            return System.Text.Json.JsonSerializer.Serialize(slideData);
        }

        /// <summary>
        /// 生成滑动验证码的键盘数据
        /// </summary>
        private string GenerateSlideKeyboardData()
        {
            var keyboardData = new
            {
                keyPresses = 0, // 滑动验证码通常不涉及键盘输入
                focusEvents = 2, // 焦点进入和离开
                inputEvents = 0
            };

            return System.Text.Json.JsonSerializer.Serialize(keyboardData);
        }

        /// <summary>
        /// 生成滑动验证码的行为数据
        /// </summary>
        private string GenerateSlideBehaviorData(KeyValuePair<string, int> trajectories, long currentTime)
        {
            var behaviorData = new
            {
                type = "slide",
                startTime = currentTime - trajectories.Value,
                endTime = currentTime,
                trajectory = trajectories.Key,
                duration = trajectories.Value,
                completed = true,
                attempts = 1
            };

            return System.Text.Json.JsonSerializer.Serialize(behaviorData);
        }

        /// <summary>
        /// 生成触摸操作数据
        /// </summary>
        private string GenerateTouchOperations()
        {
            var touchData = new
            {
                touchEvents = 0,
                touchStart = new { x = 0, y = 0, timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() },
                touchEnd = new { x = 0, y = 0, timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() },
                touchDuration = 0
            };
            return System.Text.Json.JsonSerializer.Serialize(touchData);
        }

        /// <summary>
        /// 生成表单数据信息
        /// </summary>
        private string GenerateFormDataInfo()
        {
            var formData = new
            {
                formFields = 0,
                inputEvents = 0,
                changeEvents = 0,
                submitEvents = 0
            };
            return System.Text.Json.JsonSerializer.Serialize(formData);
        }

        /// <summary>
        /// 生成行为队列数据
        /// </summary>
        private string GenerateBehaviorQueueData()
        {
            var queueData = new
            {
                queueLength = 1,
                events = new[] { "slide_start", "slide_move", "slide_end" },
                timestamps = new[] {
                    DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - 1000,
                    DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - 500,
                    DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                }
            };
            return System.Text.Json.JsonSerializer.Serialize(queueData);
        }

        /// <summary>
        /// 生成YH数据信息
        /// </summary>
        private string GenerateYhDataInfo()
        {
            var yhData = new
            {
                yhValue = "slide_captcha",
                yhTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                yhSession = GenerateSessionId()
            };
            return System.Text.Json.JsonSerializer.Serialize(yhData);
        }

        /// <summary>
        /// 生成屏幕锁定数据
        /// </summary>
        private string GenerateScreenLockData()
        {
            var lockData = new
            {
                isLocked = false,
                lockDuration = 0,
                unlockTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
            return System.Text.Json.JsonSerializer.Serialize(lockData);
        }

        /// <summary>
        /// 构建完整的验证URL
        /// </summary>
        public string BuildVerifyUrl(CaptchaParameters parameters, string baseUrl = "https://captcha1.fengkongcloud.cn/ca/v2/fverify")
        {
            var queryParams = new List<string>
            {
                $"organization={Uri.EscapeDataString(parameters.Organization)}",
                $"ny={Uri.EscapeDataString(parameters.Ny)}",
                $"protocol={parameters.Protocol}",
                $"ostype={parameters.OsType}",
                $"gg={Uri.EscapeDataString(parameters.Gg)}",
                $"callback={parameters.Callback}",
                $"hg={Uri.EscapeDataString(parameters.Hg)}",
                $"rid={parameters.Rid}",
                $"qt={Uri.EscapeDataString(parameters.Qt)}",
                $"rversion={parameters.RVersion}",
                $"sdkver={parameters.SdkVer}",
                $"th={Uri.EscapeDataString(parameters.Th)}",
                $"bs={Uri.EscapeDataString(parameters.Bs)}",
                $"sl={Uri.EscapeDataString(parameters.Sl)}",
                $"act.os={parameters.ActOs}",
                $"captchaUuid={parameters.CaptchaUuid}",
                $"fm={Uri.EscapeDataString(parameters.Fm)}",
                $"bq={Uri.EscapeDataString(parameters.Bq)}",
                $"to={Uri.EscapeDataString(parameters.To)}",
                $"yh={Uri.EscapeDataString(parameters.Yh)}",
                $"lf={Uri.EscapeDataString(parameters.Lf)}"
            };

            return $"{baseUrl}?{string.Join("&", queryParams)}";
        }
    }
}

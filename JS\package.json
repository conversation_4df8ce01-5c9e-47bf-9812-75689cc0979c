{"name": "captcha-deobfuscator", "version": "1.0.0", "description": "专门用于反混淆 captcha-sdk.min.js 的工具", "main": "deobfuscator.js", "scripts": {"start": "node deobfuscator.js", "deobfuscate": "node deobfuscator.js"}, "dependencies": {"@babel/core": "^7.22.0", "@babel/types": "^7.22.0", "@babel/traverse": "^7.22.0", "@babel/generator": "^7.22.0", "@babel/parser": "^7.22.0"}, "devDependencies": {}, "keywords": ["deobfuscation", "javascript", "ast", "<PERSON><PERSON>a", "reverse-engineering"], "author": "", "license": "MIT"}